defmodule Drops.Relation.Loaded do
  @moduledoc """
  Represents a loaded paginated result set with metadata.

  This struct contains both the loaded data and pagination metadata, making it
  easy to work with paginated results and generate navigation elements.

  ## Structure

  - `data` - List of loaded records
  - `page` - Current page number (1-based)
  - `per_page` - Number of records per page
  - `total_count` - Total number of records in the dataset
  - `total_pages` - Total number of pages
  - `has_next` - Whether there is a next page
  - `has_prev` - Whether there is a previous page

  ## Examples

      # Basic usage
      loaded = Users.page(2) |> Users.per_page(10)

      # Access data
      users = Enum.to_list(loaded)
      first_user = Enum.at(loaded, 0)

      # Access metadata
      loaded.page         # => 2
      loaded.per_page     # => 10
      loaded.total_count  # => 150
      loaded.total_pages  # => 15
      loaded.has_next     # => true
      loaded.has_prev     # => true

      # Generate navigation
      if loaded.has_prev do
        link("Previous", to: "/users?page=\#{loaded.page - 1}")
      end

      if loaded.has_next do
        link("Next", to: "/users?page=\#{loaded.page + 1}")
      end

  ## Enumerable Protocol

  The Loaded struct implements the Enumerable protocol, allowing you to use
  all Enum functions directly on paginated results:

      loaded = Users.page(1)

      # Use Enum functions
      names = Enum.map(loaded, & &1.name)
      count = Enum.count(loaded)
      first = Enum.at(loaded, 0)

      # Convert to list
      users_list = Enum.to_list(loaded)

  ## Navigation Helpers

  The struct provides convenient boolean flags for navigation:

      # Check if navigation is needed
      if loaded.total_pages > 1 do
        render_pagination(loaded)
      end

      # Generate page range
      start_page = max(1, loaded.page - 2)
      end_page = min(loaded.total_pages, loaded.page + 2)
      page_range = start_page..end_page
  """

  @type t :: %__MODULE__{
          data: [struct()],
          page: pos_integer(),
          per_page: pos_integer(),
          total_count: non_neg_integer(),
          total_pages: non_neg_integer(),
          has_next: boolean(),
          has_prev: boolean()
        }

  defstruct [
    :data,
    :page,
    :per_page,
    :total_count,
    :total_pages,
    :has_next,
    :has_prev
  ]

  @doc """
  Creates a new Loaded struct with the given data and pagination metadata.

  ## Parameters

  - `data` - List of loaded records
  - `page` - Current page number (1-based)
  - `per_page` - Number of records per page
  - `total_count` - Total number of records in the dataset

  ## Returns

  Returns a Loaded struct with calculated pagination metadata.

  ## Examples

      data = [%User{id: 1}, %User{id: 2}]
      loaded = Drops.Relation.Loaded.new(data, 1, 10, 25)

      loaded.page         # => 1
      loaded.per_page     # => 10
      loaded.total_count  # => 25
      loaded.total_pages  # => 3
      loaded.has_next     # => true
      loaded.has_prev     # => false
  """
  @spec new([struct()], pos_integer(), pos_integer(), non_neg_integer()) :: t()
  def new(data, page, per_page, total_count)
      when is_list(data) and page > 0 and per_page > 0 and total_count >= 0 do
    total_pages = calculate_total_pages(total_count, per_page)

    %__MODULE__{
      data: data,
      page: page,
      per_page: per_page,
      total_count: total_count,
      total_pages: total_pages,
      has_next: page < total_pages,
      has_prev: page > 1
    }
  end

  @doc """
  Calculates the total number of pages for the given total count and per page.

  ## Parameters

  - `total_count` - Total number of records
  - `per_page` - Number of records per page

  ## Returns

  Returns the total number of pages as a positive integer.

  ## Examples

      Drops.Relation.Loaded.calculate_total_pages(25, 10)  # => 3
      Drops.Relation.Loaded.calculate_total_pages(30, 10)  # => 3
      Drops.Relation.Loaded.calculate_total_pages(0, 10)   # => 0
  """
  @spec calculate_total_pages(non_neg_integer(), pos_integer()) :: non_neg_integer()
  def calculate_total_pages(total_count, per_page) when total_count >= 0 and per_page > 0 do
    case total_count do
      0 -> 0
      _ -> ceil(total_count / per_page)
    end
  end

  @doc """
  Returns the offset for the current page.

  ## Parameters

  - `loaded` - A Loaded struct

  ## Returns

  Returns the offset (0-based) for the current page.

  ## Examples

      loaded = Drops.Relation.Loaded.new([], 1, 10, 25)
      Drops.Relation.Loaded.offset(loaded)  # => 0

      loaded = Drops.Relation.Loaded.new([], 3, 10, 25)
      Drops.Relation.Loaded.offset(loaded)  # => 20
  """
  @spec offset(t()) :: non_neg_integer()
  def offset(%__MODULE__{page: page, per_page: per_page}) do
    (page - 1) * per_page
  end

  @doc """
  Returns a range of page numbers around the current page for navigation.

  ## Parameters

  - `loaded` - A Loaded struct
  - `window` - Number of pages to show on each side of current page (default: 2)

  ## Returns

  Returns a range of page numbers suitable for pagination navigation.

  ## Examples

      loaded = Drops.Relation.Loaded.new([], 5, 10, 100)
      Drops.Relation.Loaded.page_range(loaded)     # => 3..7
      Drops.Relation.Loaded.page_range(loaded, 1)  # => 4..6
  """
  @spec page_range(t(), non_neg_integer()) :: Range.t()
  def page_range(%__MODULE__{page: page, total_pages: total_pages}, window \\ 2) do
    start_page = max(1, page - window)
    end_page = min(total_pages, page + window)
    start_page..end_page
  end
end

# Enumerable protocol implementation for Loaded
defimpl Enumerable, for: Drops.Relation.Loaded do
  @moduledoc """
  Enumerable protocol implementation for Drops.Relation.Loaded.

  This allows Loaded structs to be used with all Enum functions,
  operating on the loaded data while preserving pagination metadata.
  """

  def count(%Drops.Relation.Loaded{data: data}) do
    {:ok, length(data)}
  end

  def member?(%Drops.Relation.Loaded{data: data}, element) do
    {:ok, element in data}
  end

  def slice(%Drops.Relation.Loaded{data: data}) do
    size = length(data)
    {:ok, size, fn start, count, _step -> Enum.slice(data, start, count) end}
  end

  def reduce(%Drops.Relation.Loaded{data: data}, acc, fun) do
    Enumerable.List.reduce(data, acc, fun)
  end
end
