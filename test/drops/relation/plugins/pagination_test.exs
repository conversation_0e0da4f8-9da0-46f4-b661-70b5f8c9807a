defmodule Drops.Relation.Plugins.PaginationTest do
  use Drops.RelationCase, async: false

  alias Drops.Relation.Loaded

  describe "basic pagination" do
    @tag relations: [:users]
    test "page/1 returns first page with default per_page", %{users: users} do
      # Insert test data
      insert_test_users(users, 25)

      # Test first page
      loaded = users.page(1)

      assert %Loaded{} = loaded
      assert loaded.page == 1
      # default per_page
      assert loaded.per_page == 20
      assert loaded.total_count == 25
      assert loaded.total_pages == 2
      assert loaded.has_next == true
      assert loaded.has_prev == false
      assert length(loaded.data) == 20
    end

    @tag relations: [:users]
    test "page/2 returns page with custom per_page", %{users: users} do
      # Insert test data
      insert_test_users(users, 25)

      # Test first page with custom per_page
      loaded = users.page(1, 10)

      assert %Loaded{} = loaded
      assert loaded.page == 1
      assert loaded.per_page == 10
      assert loaded.total_count == 25
      assert loaded.total_pages == 3
      assert loaded.has_next == true
      assert loaded.has_prev == false
      assert length(loaded.data) == 10
    end

    @tag relations: [:users]
    test "page/2 returns correct page in middle", %{users: users} do
      # Insert test data
      insert_test_users(users, 25)

      # Test middle page
      loaded = users.page(2, 10)

      assert %Loaded{} = loaded
      assert loaded.page == 2
      assert loaded.per_page == 10
      assert loaded.total_count == 25
      assert loaded.total_pages == 3
      assert loaded.has_next == true
      assert loaded.has_prev == true
      assert length(loaded.data) == 10
    end

    @tag relations: [:users]
    test "page/2 returns last page correctly", %{users: users} do
      # Insert test data
      insert_test_users(users, 25)

      # Test last page
      loaded = users.page(3, 10)

      assert %Loaded{} = loaded
      assert loaded.page == 3
      assert loaded.per_page == 10
      assert loaded.total_count == 25
      assert loaded.total_pages == 3
      assert loaded.has_next == false
      assert loaded.has_prev == true
      # remaining records
      assert length(loaded.data) == 5
    end
  end

  describe "per_page functionality" do
    @tag relations: [:users]
    test "per_page/1 sets per_page for subsequent pagination", %{users: users} do
      # Insert test data
      insert_test_users(users, 25)

      # Set per_page first
      relation = users.per_page(5)
      loaded = users.page(relation, 1)

      assert %Loaded{} = loaded
      assert loaded.page == 1
      assert loaded.per_page == 5
      assert loaded.total_count == 25
      assert loaded.total_pages == 5
      assert length(loaded.data) == 5
    end

    @tag relations: [:users]
    test "per_page/2 works with existing relations", %{users: users} do
      # Insert test data
      insert_test_users(users, 25)

      # Create base query and add per_page
      base_query = users.restrict(active: true)
      paginated_query = users.per_page(base_query, 3)
      loaded = users.page(paginated_query, 1)

      assert %Loaded{} = loaded
      assert loaded.page == 1
      assert loaded.per_page == 3
      # Should only count active users
      # All test users are active
      assert loaded.total_count == 25
      assert length(loaded.data) == 3
    end
  end

  describe "chaining with other operations" do
    @tag relations: [:users]
    test "works with restrict operations", %{users: users} do
      # Insert mixed test data
      insert_test_users(users, 10, active: true)
      insert_test_users(users, 5, active: false)

      # Chain restrict with pagination
      loaded =
        users
        |> users.restrict(active: true)
        |> users.per_page(5)
        |> users.page(1)

      assert %Loaded{} = loaded
      assert loaded.page == 1
      assert loaded.per_page == 5
      # Only active users
      assert loaded.total_count == 10
      assert loaded.total_pages == 2
      assert length(loaded.data) == 5

      # Verify all returned users are active
      assert Enum.all?(loaded.data, & &1.active)
    end

    @tag relations: [:users]
    test "works with order operations", %{users: users} do
      # Insert test data with specific names for ordering
      {:ok, _} = users.insert(%{name: "Charlie", email: "<EMAIL>", active: true})
      {:ok, _} = users.insert(%{name: "Alice", email: "<EMAIL>", active: true})
      {:ok, _} = users.insert(%{name: "Bob", email: "<EMAIL>", active: true})

      # Chain order with pagination
      loaded =
        users
        |> users.order(:name)
        |> users.per_page(2)
        |> users.page(1)

      assert %Loaded{} = loaded
      assert loaded.page == 1
      assert loaded.per_page == 2
      assert loaded.total_count == 3
      assert loaded.total_pages == 2
      assert length(loaded.data) == 2

      # Verify ordering
      [first, second] = loaded.data
      assert first.name == "Alice"
      assert second.name == "Bob"
    end
  end

  describe "edge cases" do
    @tag relations: [:users]
    test "handles empty table", %{users: users} do
      loaded = users.page(1)

      assert %Loaded{} = loaded
      assert loaded.page == 1
      assert loaded.per_page == 20
      assert loaded.total_count == 0
      assert loaded.total_pages == 0
      assert loaded.has_next == false
      assert loaded.has_prev == false
      assert loaded.data == []
    end

    @tag relations: [:users]
    test "handles page beyond available data", %{users: users} do
      # Insert only 5 records
      insert_test_users(users, 5)

      # Request page 2 with per_page 10 (should be empty)
      loaded = users.page(2, 10)

      assert %Loaded{} = loaded
      assert loaded.page == 2
      assert loaded.per_page == 10
      assert loaded.total_count == 5
      assert loaded.total_pages == 1
      assert loaded.has_next == false
      assert loaded.has_prev == true
      assert loaded.data == []
    end

    @tag relations: [:users]
    test "handles single record", %{users: users} do
      # Insert single record
      {:ok, _} = users.insert(%{name: "Single User", email: "<EMAIL>", active: true})

      loaded = users.page(1, 10)

      assert %Loaded{} = loaded
      assert loaded.page == 1
      assert loaded.per_page == 10
      assert loaded.total_count == 1
      assert loaded.total_pages == 1
      assert loaded.has_next == false
      assert loaded.has_prev == false
      assert length(loaded.data) == 1
    end
  end

  describe "Enumerable protocol" do
    @tag relations: [:users]
    test "implements Enumerable.count/1", %{users: users} do
      insert_test_users(users, 15)
      loaded = users.page(1, 10)

      assert Enum.count(loaded) == 10
    end

    @tag relations: [:users]
    test "implements Enumerable.member?/2", %{users: users} do
      insert_test_users(users, 5)
      loaded = users.page(1, 10)

      [first_user | _] = loaded.data
      assert Enum.member?(loaded, first_user)

      # Create a user that's not in the loaded data
      {:ok, other_user} = users.insert(%{name: "Other", email: "<EMAIL>", active: true})
      refute Enum.member?(loaded, other_user)
    end

    @tag relations: [:users]
    test "implements Enumerable.reduce/3", %{users: users} do
      insert_test_users(users, 5)
      loaded = users.page(1, 10)

      # Test map
      names = Enum.map(loaded, & &1.name)
      assert length(names) == 5
      assert Enum.all?(names, &is_binary/1)

      # Test filter
      filtered = Enum.filter(loaded, &String.contains?(&1.name, "User"))
      assert length(filtered) == 5

      # Test reduce
      name_list = Enum.reduce(loaded, [], fn user, acc -> [user.name | acc] end)
      assert length(name_list) == 5
    end

    @tag relations: [:users]
    test "works with Enum.to_list/1", %{users: users} do
      insert_test_users(users, 5)
      loaded = users.page(1, 10)

      user_list = Enum.to_list(loaded)
      assert length(user_list) == 5
      assert user_list == loaded.data
    end

    @tag relations: [:users]
    test "works with Enum.at/2", %{users: users} do
      insert_test_users(users, 5)
      loaded = users.page(1, 10)

      first_user = Enum.at(loaded, 0)
      assert first_user == List.first(loaded.data)

      last_user = Enum.at(loaded, 4)
      assert last_user == List.last(loaded.data)

      assert Enum.at(loaded, 10) == nil
    end
  end

  describe "Loaded struct helpers" do
    test "calculate_total_pages/2 works correctly" do
      assert Loaded.calculate_total_pages(0, 10) == 0
      assert Loaded.calculate_total_pages(1, 10) == 1
      assert Loaded.calculate_total_pages(10, 10) == 1
      assert Loaded.calculate_total_pages(11, 10) == 2
      assert Loaded.calculate_total_pages(25, 10) == 3
      assert Loaded.calculate_total_pages(30, 10) == 3
    end

    test "offset/1 calculates correct offset" do
      loaded1 = Loaded.new([], 1, 10, 25)
      assert Loaded.offset(loaded1) == 0

      loaded2 = Loaded.new([], 2, 10, 25)
      assert Loaded.offset(loaded2) == 10

      loaded3 = Loaded.new([], 3, 10, 25)
      assert Loaded.offset(loaded3) == 20
    end

    test "page_range/2 generates correct page ranges" do
      # Test with default window (2)
      # 10 total pages
      loaded = Loaded.new([], 5, 10, 100)
      assert Loaded.page_range(loaded) == 3..7

      # Test with custom window
      assert Loaded.page_range(loaded, 1) == 4..6

      # Test at beginning
      loaded_start = Loaded.new([], 1, 10, 100)
      assert Loaded.page_range(loaded_start) == 1..3

      # Test at end
      loaded_end = Loaded.new([], 10, 10, 100)
      assert Loaded.page_range(loaded_end) == 8..10

      # Test with small total pages
      # 3 total pages
      loaded_small = Loaded.new([], 2, 10, 25)
      assert Loaded.page_range(loaded_small) == 1..3
    end

    test "new/4 creates correct Loaded struct" do
      data = [%{id: 1}, %{id: 2}]
      loaded = Loaded.new(data, 2, 10, 25)

      assert loaded.data == data
      assert loaded.page == 2
      assert loaded.per_page == 10
      assert loaded.total_count == 25
      assert loaded.total_pages == 3
      assert loaded.has_next == true
      assert loaded.has_prev == true
    end

    test "new/4 handles edge cases correctly" do
      # First page
      loaded_first = Loaded.new([], 1, 10, 25)
      assert loaded_first.has_prev == false
      assert loaded_first.has_next == true

      # Last page
      loaded_last = Loaded.new([], 3, 10, 25)
      assert loaded_last.has_prev == true
      assert loaded_last.has_next == false

      # Only page
      loaded_only = Loaded.new([], 1, 10, 5)
      assert loaded_only.has_prev == false
      assert loaded_only.has_next == false
      assert loaded_only.total_pages == 1

      # Empty dataset
      loaded_empty = Loaded.new([], 1, 10, 0)
      assert loaded_empty.has_prev == false
      assert loaded_empty.has_next == false
      assert loaded_empty.total_pages == 0
    end
  end

  # Helper functions

  defp insert_test_users(users, count, attrs \\ []) do
    default_attrs = [active: true]
    attrs = Keyword.merge(default_attrs, attrs)

    # Get current count to ensure unique emails
    current_count = users.count()

    for i <- 1..count do
      unique_id = current_count + i

      user_attrs = %{
        name: "User #{unique_id}",
        email: "user#{unique_id}@example.com",
        active: attrs[:active]
      }

      {:ok, _} = users.insert(user_attrs)
    end
  end
end
